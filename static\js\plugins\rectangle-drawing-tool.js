// --- Rectangle Drawing Tool Plugin ---

/**
 * Rectangle Drawing Tool Plugin for Lightweight Charts
 * Allows users to draw rectangles on the chart using crosshair coordinates
 */

// Rectangle data structure
class Rectangle {
    constructor(id, startPoint, endPoint, options = {}) {
        this.id = id;
        this.startPoint = startPoint; // { time, price }
        this.endPoint = endPoint;     // { time, price }
        this.options = {
            fillColor: options.fillColor || 'rgba(41, 98, 255, 0.1)',
            borderColor: options.borderColor || '#2962FF',
            borderWidth: options.borderWidth || 1,
            borderStyle: options.borderStyle || 'solid',
            ...options
        };
        this.isSelected = false;
        this.isHovered = false;
    }

    // Get normalized coordinates (top-left, bottom-right)
    getNormalizedCoords() {
        const left = Math.min(this.startPoint.time, this.endPoint.time);
        const right = Math.max(this.startPoint.time, this.endPoint.time);
        const top = Math.max(this.startPoint.price, this.endPoint.price);
        const bottom = Math.min(this.startPoint.price, this.endPoint.price);
        
        return {
            left,
            right,
            top,
            bottom,
            width: right - left,
            height: top - bottom
        };
    }

    // Check if a point is inside the rectangle
    containsPoint(time, price) {
        const coords = this.getNormalizedCoords();
        return time >= coords.left && time <= coords.right && 
               price >= coords.bottom && price <= coords.top;
    }

    // Check if a point is near the border (for selection)
    isNearBorder(time, price, tolerance = 0.01) {
        const coords = this.getNormalizedCoords();
        const timeRange = coords.width;
        const priceRange = coords.height;
        
        const timeTolerance = timeRange * tolerance;
        const priceTolerance = priceRange * tolerance;
        
        // Check if near any border
        const nearLeft = Math.abs(time - coords.left) <= timeTolerance;
        const nearRight = Math.abs(time - coords.right) <= timeTolerance;
        const nearTop = Math.abs(price - coords.top) <= priceTolerance;
        const nearBottom = Math.abs(price - coords.bottom) <= priceTolerance;
        
        const withinTimeRange = time >= coords.left - timeTolerance && time <= coords.right + timeTolerance;
        const withinPriceRange = price >= coords.bottom - priceTolerance && price <= coords.top + priceTolerance;
        
        return (nearLeft || nearRight) && withinPriceRange || 
               (nearTop || nearBottom) && withinTimeRange;
    }
}

// Rectangle Drawing Tool Manager
class RectangleDrawingTool {
    constructor() {
        this.rectangles = new Map();
        this.isDrawing = false;
        this.isEnabled = false;
        this.currentRectangle = null;
        this.selectedRectangle = null;
        this.nextId = 1;
        this.chart = null;
        this.container = null;
        
        // Drawing state
        this.drawingStartPoint = null;
        this.tempRectangle = null;
        
        // Event handlers
        this.onMouseMove = this.onMouseMove.bind(this);
        this.onClick = this.onClick.bind(this);
        this.onKeyDown = this.onKeyDown.bind(this);
        
        // Crosshair tracking
        this.currentCrosshairPosition = null;
    }

    // Initialize the drawing tool with a chart
    initialize(chart, container) {
        this.chart = chart;
        this.container = container;
        
        // Subscribe to crosshair events
        this.chart.subscribeCrosshairMove(this.onCrosshairMove.bind(this));
        
        return this;
    }

    // Enable/disable the drawing tool
    setEnabled(enabled) {
        if (this.isEnabled === enabled) return;
        
        this.isEnabled = enabled;
        
        if (enabled) {
            this.container.addEventListener('click', this.onClick);
            this.container.addEventListener('mousemove', this.onMouseMove);
            this.container.addEventListener('contextmenu', this.onContextMenu.bind(this));
            document.addEventListener('keydown', this.onKeyDown);
            this.container.style.cursor = 'crosshair';
        } else {
            this.container.removeEventListener('click', this.onClick);
            this.container.removeEventListener('mousemove', this.onMouseMove);
            this.container.removeEventListener('contextmenu', this.onContextMenu.bind(this));
            document.removeEventListener('keydown', this.onKeyDown);
            this.container.style.cursor = 'default';

            // Cancel any ongoing drawing
            this.cancelDrawing();
        }
    }

    // Handle crosshair movement
    onCrosshairMove(param) {
        if (!param || typeof param.time === 'undefined' || !param.point) {
            this.currentCrosshairPosition = null;
            return;
        }

        // Get price from the series data or calculate from coordinate
        let price = 0;
        if (param.seriesData && param.seriesData.size > 0) {
            // Try to get price from any available series data
            for (const [series, data] of param.seriesData) {
                if (data) {
                    // For candlestick data, use close price; for other data types, use value
                    price = data.close || data.value || 0;
                    if (price !== 0) break;
                }
            }
        }

        // If no price from series data, calculate from coordinate
        if (price === 0 && param.point) {
            const priceScale = this.chart.priceScale('right');
            price = priceScale.coordinateToPrice(param.point.y);
        }

        this.currentCrosshairPosition = {
            time: param.time,
            price: price,
            point: param.point
        };

        // Update temporary rectangle during drawing
        if (this.isDrawing && this.drawingStartPoint && this.currentCrosshairPosition) {
            this.updateTempRectangle();
        }

        // Update hover states
        this.updateHoverStates();
    }

    // Handle mouse movement
    onMouseMove() {
        if (!this.isEnabled) return;

        // Update cursor based on hover state
        let cursor = 'crosshair';
        if (this.getHoveredRectangle()) {
            cursor = 'pointer';
        }
        this.container.style.cursor = cursor;
    }

    // Handle click events
    onClick(event) {
        if (!this.isEnabled || !this.currentCrosshairPosition) return;

        event.preventDefault();
        event.stopPropagation();

        if (!this.isDrawing) {
            // Check if clicking on existing rectangle
            const clickedRectangle = this.getRectangleAtPosition(
                this.currentCrosshairPosition.time,
                this.currentCrosshairPosition.price
            );

            if (clickedRectangle) {
                this.selectRectangle(clickedRectangle);
                return;
            }

            // Start drawing new rectangle
            this.startDrawing();
        } else {
            // Finish drawing
            this.finishDrawing();
        }
    }

    // Handle keyboard events
    onKeyDown(event) {
        if (!this.isEnabled) return;

        switch (event.key) {
            case 'Escape':
                if (this.isDrawing) {
                    this.cancelDrawing();
                } else if (this.selectedRectangle) {
                    this.deselectAll();
                }
                break;
            case 'Delete':
            case 'Backspace':
                if (this.selectedRectangle) {
                    this.deleteRectangle(this.selectedRectangle.id);
                }
                break;
        }
    }

    // Handle context menu (right-click)
    onContextMenu(event) {
        if (!this.isEnabled || !this.currentCrosshairPosition) return;

        event.preventDefault();

        const clickedRectangle = this.getRectangleAtPosition(
            this.currentCrosshairPosition.time,
            this.currentCrosshairPosition.price
        );

        if (clickedRectangle) {
            this.selectRectangle(clickedRectangle);
            this.showContextMenu(event, clickedRectangle);
        } else {
            this.showContextMenu(event, null);
        }
    }

    // Show context menu
    showContextMenu(event, rectangle) {
        // Remove any existing context menu
        const existingMenu = document.querySelector('.rectangle-context-menu');
        if (existingMenu) {
            existingMenu.remove();
        }

        const menu = document.createElement('div');
        menu.className = 'rectangle-context-menu';
        menu.style.cssText = `
            position: fixed;
            left: ${event.clientX}px;
            top: ${event.clientY}px;
            background: #2A2E39;
            border: 1px solid #363C4E;
            border-radius: 4px;
            padding: 4px 0;
            z-index: 10000;
            font-size: 12px;
            color: #D1D4DC;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            min-width: 120px;
        `;

        const menuItems = [];

        if (rectangle) {
            menuItems.push(
                { text: 'Delete Rectangle', action: () => this.deleteRectangle(rectangle.id) },
                { text: 'Change Color', action: () => this.showColorPicker(rectangle) },
                { text: 'Duplicate', action: () => this.duplicateRectangle(rectangle) }
            );
        }

        menuItems.push(
            { text: 'Clear All', action: () => this.clearAll() },
            { text: 'Exit Drawing Mode', action: () => this.setEnabled(false) }
        );

        menuItems.forEach((item, index) => {
            if (index > 0 && (index === menuItems.length - 2)) {
                const separator = document.createElement('div');
                separator.style.cssText = 'height: 1px; background: #363C4E; margin: 4px 0;';
                menu.appendChild(separator);
            }

            const menuItem = document.createElement('div');
            menuItem.textContent = item.text;
            menuItem.style.cssText = `
                padding: 6px 12px;
                cursor: pointer;
                transition: background-color 0.1s;
            `;

            menuItem.addEventListener('mouseenter', () => {
                menuItem.style.backgroundColor = '#363C4E';
            });

            menuItem.addEventListener('mouseleave', () => {
                menuItem.style.backgroundColor = 'transparent';
            });

            menuItem.addEventListener('click', () => {
                item.action();
                menu.remove();
            });

            menu.appendChild(menuItem);
        });

        document.body.appendChild(menu);

        // Close menu when clicking outside
        const closeMenu = (e) => {
            if (!menu.contains(e.target)) {
                menu.remove();
                document.removeEventListener('click', closeMenu);
            }
        };

        setTimeout(() => {
            document.addEventListener('click', closeMenu);
        }, 0);
    }

    // Start drawing a new rectangle
    startDrawing() {
        if (!this.currentCrosshairPosition) return;

        this.isDrawing = true;
        this.drawingStartPoint = {
            time: this.currentCrosshairPosition.time,
            price: this.currentCrosshairPosition.price
        };

        // Deselect any selected rectangle
        this.deselectAll();

        // Create temporary rectangle for preview
        this.tempRectangle = new Rectangle(
            'temp',
            this.drawingStartPoint,
            this.drawingStartPoint,
            { fillColor: 'rgba(41, 98, 255, 0.05)', borderColor: '#2962FF' }
        );
    }

    // Update temporary rectangle during drawing
    updateTempRectangle() {
        if (!this.tempRectangle || !this.currentCrosshairPosition) return;

        this.tempRectangle.endPoint = {
            time: this.currentCrosshairPosition.time,
            price: this.currentCrosshairPosition.price
        };

        // Trigger chart redraw
        this.chart.requestUpdate();
    }

    // Finish drawing the rectangle
    finishDrawing() {
        if (!this.isDrawing || !this.drawingStartPoint || !this.currentCrosshairPosition) return;

        const endPoint = {
            time: this.currentCrosshairPosition.time,
            price: this.currentCrosshairPosition.price
        };

        // Check if rectangle has minimum size
        const timeDiff = Math.abs(endPoint.time - this.drawingStartPoint.time);
        const priceDiff = Math.abs(endPoint.price - this.drawingStartPoint.price);

        if (timeDiff > 0 && priceDiff > 0) {
            // Create the actual rectangle
            const rectangle = new Rectangle(
                this.nextId++,
                this.drawingStartPoint,
                endPoint
            );

            this.rectangles.set(rectangle.id, rectangle);
            this.selectRectangle(rectangle);
        }

        // Clean up drawing state
        this.isDrawing = false;
        this.drawingStartPoint = null;
        this.tempRectangle = null;

        // Trigger chart redraw
        this.chart.requestUpdate();
    }

    // Cancel drawing
    cancelDrawing() {
        this.isDrawing = false;
        this.drawingStartPoint = null;
        this.tempRectangle = null;
        this.chart.requestUpdate();
    }

    // Get rectangle at position
    getRectangleAtPosition(time, price) {
        for (const rectangle of this.rectangles.values()) {
            if (rectangle.containsPoint(time, price) || rectangle.isNearBorder(time, price)) {
                return rectangle;
            }
        }
        return null;
    }

    // Get hovered rectangle
    getHoveredRectangle() {
        for (const rectangle of this.rectangles.values()) {
            if (rectangle.isHovered) {
                return rectangle;
            }
        }
        return null;
    }

    // Update hover states
    updateHoverStates() {
        if (!this.currentCrosshairPosition) return;

        for (const rectangle of this.rectangles.values()) {
            const wasHovered = rectangle.isHovered;
            rectangle.isHovered = rectangle.containsPoint(
                this.currentCrosshairPosition.time,
                this.currentCrosshairPosition.price
            ) || rectangle.isNearBorder(
                this.currentCrosshairPosition.time,
                this.currentCrosshairPosition.price
            );

            if (wasHovered !== rectangle.isHovered) {
                this.chart.requestUpdate();
            }
        }
    }

    // Select a rectangle
    selectRectangle(rectangle) {
        this.deselectAll();
        rectangle.isSelected = true;
        this.selectedRectangle = rectangle;
        this.chart.requestUpdate();
    }

    // Deselect all rectangles
    deselectAll() {
        for (const rectangle of this.rectangles.values()) {
            rectangle.isSelected = false;
        }
        this.selectedRectangle = null;
        this.chart.requestUpdate();
    }

    // Delete a rectangle
    deleteRectangle(id) {
        if (this.rectangles.has(id)) {
            this.rectangles.delete(id);
            if (this.selectedRectangle && this.selectedRectangle.id === id) {
                this.selectedRectangle = null;
            }
            this.chart.requestUpdate();
        }
    }

    // Clear all rectangles
    clearAll() {
        this.rectangles.clear();
        this.selectedRectangle = null;
        this.cancelDrawing();
        this.chart.requestUpdate();
    }

    // Get all rectangles
    getAllRectangles() {
        return Array.from(this.rectangles.values());
    }

    // Duplicate a rectangle
    duplicateRectangle(rectangle) {
        const offset = 0.1; // Small offset for the duplicate
        const coords = rectangle.getNormalizedCoords();
        const timeOffset = coords.width * offset;
        const priceOffset = coords.height * offset;

        const newRectangle = new Rectangle(
            this.nextId++,
            {
                time: rectangle.startPoint.time + timeOffset,
                price: rectangle.startPoint.price + priceOffset
            },
            {
                time: rectangle.endPoint.time + timeOffset,
                price: rectangle.endPoint.price + priceOffset
            },
            { ...rectangle.options }
        );

        this.rectangles.set(newRectangle.id, newRectangle);
        this.selectRectangle(newRectangle);
        this.chart.requestUpdate();
    }

    // Show color picker for rectangle
    showColorPicker(rectangle) {
        const colorPicker = document.createElement('input');
        colorPicker.type = 'color';
        colorPicker.value = rectangle.options.borderColor;
        colorPicker.style.position = 'absolute';
        colorPicker.style.left = '-9999px';

        colorPicker.addEventListener('change', (e) => {
            const newColor = e.target.value;
            rectangle.options.borderColor = newColor;
            rectangle.options.fillColor = newColor + '1A'; // Add transparency
            this.chart.requestUpdate();
            colorPicker.remove();
        });

        document.body.appendChild(colorPicker);
        colorPicker.click();
    }

    // Cleanup
    destroy() {
        this.setEnabled(false);
        this.clearAll();
        this.chart = null;
        this.container = null;
    }
}

// Rectangle Renderer for drawing rectangles on the chart
class RectangleRenderer {
    constructor(drawingTool) {
        this.drawingTool = drawingTool;
        this._data = null;
        this._options = null;
    }

    draw(target, priceToCoordinate) {
        target.useBitmapCoordinateSpace(scope => this._drawImpl(scope, priceToCoordinate));
    }

    update(data, options) {
        this._data = data;
        this._options = options;
    }

    // Required for pane view interface
    zOrder() {
        return 'normal';
    }

    _drawImpl(scope, priceToCoordinate) {
        if (!this.drawingTool || !this.drawingTool.chart) return;

        const ctx = scope.context;
        ctx.save();

        try {
            // Draw all rectangles
            const rectangles = this.drawingTool.getAllRectangles();
            for (const rectangle of rectangles) {
                this._drawRectangle(ctx, rectangle, priceToCoordinate, scope);
            }

            // Draw temporary rectangle during drawing
            if (this.drawingTool.tempRectangle) {
                this._drawRectangle(ctx, this.drawingTool.tempRectangle, priceToCoordinate, scope);
            }
        } finally {
            ctx.restore();
        }
    }

    _drawRectangle(ctx, rectangle, priceToCoordinate, scope) {
        const coords = rectangle.getNormalizedCoords();
        const timeScale = this.drawingTool.chart.timeScale();

        // Convert time to x coordinates
        const leftX = timeScale.timeToCoordinate(coords.left);
        const rightX = timeScale.timeToCoordinate(coords.right);

        // Convert price to y coordinates
        const topY = priceToCoordinate(coords.top);
        const bottomY = priceToCoordinate(coords.bottom);

        if (leftX === null || rightX === null || topY === null || bottomY === null) return;

        // Apply device pixel ratio
        const x = leftX * scope.horizontalPixelRatio;
        const y = topY * scope.verticalPixelRatio;
        const width = (rightX - leftX) * scope.horizontalPixelRatio;
        const height = (bottomY - topY) * scope.verticalPixelRatio;

        // Draw fill
        if (rectangle.options.fillColor) {
            ctx.fillStyle = rectangle.options.fillColor;
            ctx.fillRect(x, y, width, height);
        }

        // Draw border
        if (rectangle.options.borderColor && rectangle.options.borderWidth > 0) {
            ctx.strokeStyle = rectangle.options.borderColor;
            ctx.lineWidth = rectangle.options.borderWidth * scope.verticalPixelRatio;

            // Set line style
            if (rectangle.options.borderStyle === 'dashed') {
                ctx.setLineDash([5 * scope.verticalPixelRatio, 5 * scope.verticalPixelRatio]);
            } else {
                ctx.setLineDash([]);
            }

            ctx.strokeRect(x, y, width, height);
        }

        // Draw selection indicators
        if (rectangle.isSelected) {
            this._drawSelectionIndicators(ctx, x, y, width, height, scope);
        }

        // Draw hover effect
        if (rectangle.isHovered && !rectangle.isSelected) {
            this._drawHoverEffect(ctx, x, y, width, height, scope);
        }
    }

    _drawSelectionIndicators(ctx, x, y, width, height, scope) {
        const handleSize = 6 * scope.verticalPixelRatio;
        const halfHandle = handleSize / 2;

        ctx.fillStyle = '#2962FF';
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 1 * scope.verticalPixelRatio;
        ctx.setLineDash([]);

        // Draw corner handles
        const handles = [
            [x - halfHandle, y - halfHandle], // top-left
            [x + width - halfHandle, y - halfHandle], // top-right
            [x - halfHandle, y + height - halfHandle], // bottom-left
            [x + width - halfHandle, y + height - halfHandle] // bottom-right
        ];

        for (const [hx, hy] of handles) {
            ctx.fillRect(hx, hy, handleSize, handleSize);
            ctx.strokeRect(hx, hy, handleSize, handleSize);
        }
    }

    _drawHoverEffect(ctx, x, y, width, height, scope) {
        ctx.strokeStyle = 'rgba(41, 98, 255, 0.8)';
        ctx.lineWidth = 2 * scope.verticalPixelRatio;
        ctx.setLineDash([]);
        ctx.strokeRect(x, y, width, height);
    }
}

// Rectangle Drawing Tool Custom Series
export const RectangleDrawingSeries = {
    create(drawingTool) {
        return Object.create(this, {
            _renderer: { value: new RectangleRenderer(drawingTool), writable: true },
            _drawingTool: { value: drawingTool, writable: true },
            _options: { value: null, writable: true },
            _defaultOptions: { value: null, writable: true }
        });
    },

    renderer() {
        return this._renderer;
    },

    update(data, options) {
        this._options = this._options ? Object.assign(this._options, options) : Object.assign({}, this.defaultOptions(), options);
        this._renderer.update(data, this._options);
    },

    defaultOptions() {
        if (!this._defaultOptions) {
            this._defaultOptions = {
                fillColor: 'rgba(41, 98, 255, 0.1)',
                borderColor: '#2962FF',
                borderWidth: 1,
                borderStyle: 'solid'
            };
        }
        return this._defaultOptions;
    },

    applyOptions(options) {
        this._options = Object.assign({}, this._options || this.defaultOptions(), options);
        return this;
    },

    // Required method for custom series
    paneViews() {
        return [this._renderer];
    },

    // Required method for custom series
    timeAxisViews() {
        return [];
    },

    // Required method for custom series
    priceAxisViews() {
        return [];
    },

    destroy() {
        if (this._drawingTool) {
            this._drawingTool.destroy();
        }
        this._renderer = null;
        this._drawingTool = null;
        this._options = null;
        this._defaultOptions = null;
    }
};

// Export the classes
export { Rectangle, RectangleDrawingTool, RectangleRenderer };
